/**
 * @file flash_storage.h
 * @brief STM32F407VET6 Flash存储管理头文件
 * <AUTHOR> (米醋电子工作室)
 * @version 1.0
 * @date 2025-01-30
 * 
 * @description 
 * 为STM32F407VET6设计的安全Flash存储方案
 * 使用Sector 7末尾的16字节存储2个数据的符号位和大小
 * 确保不影响程序正常运行
 */

#ifndef __FLASH_STORAGE_H
#define __FLASH_STORAGE_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include <stdint.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/
/**
 * @brief Flash操作状态枚举
 */
typedef enum {
    FLASH_STORAGE_OK = 0,           // 操作成功
    FLASH_STORAGE_ERROR,            // 操作失败
    FLASH_STORAGE_TIMEOUT,          // 操作超时
    FLASH_STORAGE_INVALID_PARAM     // 参数无效
} FlashStorage_Status;

/**
 * @brief 数据结构体
 */
typedef struct {
    uint8_t sign;       // 符号位 (0=正数, 1=负数)
    uint32_t size;      // 数据大小
} DataInfo_t;

/* Exported constants --------------------------------------------------------*/
// STM32F407VET6 Flash扇区定义
#define FLASH_SECTOR_7_BASE         0x08060000UL    // Sector 7起始地址
#define FLASH_SECTOR_7_SIZE         0x00020000UL    // Sector 7大小 (128KB)
#define FLASH_SECTOR_7_END          0x0807FFFFUL    // Sector 7结束地址

// 数据存储地址定义 (使用Sector 7的最后16字节)
#define DATA_STORAGE_BASE           0x0807FFF0UL    // 存储区起始地址
#define DATA1_SIGN_ADDR             0x0807FFF0UL    // 第一个数据符号位地址
#define DATA1_SIZE_ADDR             0x0807FFF4UL    // 第一个数据大小地址  
#define DATA2_SIGN_ADDR             0x0807FFF8UL    // 第二个数据符号位地址
#define DATA2_SIZE_ADDR             0x0807FFFCUL    // 第二个数据大小地址

// Flash操作相关常量
#define FLASH_TIMEOUT_VALUE         50000UL         // Flash操作超时值
#define FLASH_VOLTAGE_RANGE         VoltageRange_3  // 电压范围 2.7V-3.6V

/* Exported macro ------------------------------------------------------------*/
#define IS_VALID_SIGN(sign)         ((sign) <= 1)
#define IS_VALID_SIZE(size)         ((size) > 0)

/* Exported functions prototypes ---------------------------------------------*/

/**
 * @brief 初始化Flash存储模块
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_Init(void);

/**
 * @brief 写入第一个数据的信息
 * @param sign: 符号位 (0=正数, 1=负数)
 * @param size: 数据大小
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_WriteData1(uint8_t sign, uint32_t size);

/**
 * @brief 写入第二个数据的信息  
 * @param sign: 符号位 (0=正数, 1=负数)
 * @param size: 数据大小
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_WriteData2(uint8_t sign, uint32_t size);

/**
 * @brief 读取第一个数据的信息
 * @param data_info: 指向数据信息结构体的指针
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_ReadData1(DataInfo_t *data_info);

/**
 * @brief 读取第二个数据的信息
 * @param data_info: 指向数据信息结构体的指针  
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_ReadData2(DataInfo_t *data_info);

/**
 * @brief 擦除存储区域
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_EraseStorage(void);

/**
 * @brief 检查存储区域是否为空
 * @retval bool true=空, false=非空
 */
bool FlashStorage_IsEmpty(void);

/**
 * @brief 获取Flash存储区域状态信息
 * @param data1_info: 第一个数据信息
 * @param data2_info: 第二个数据信息
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_GetStatus(DataInfo_t *data1_info, DataInfo_t *data2_info);

#ifdef __cplusplus
}
#endif

#endif /* __FLASH_STORAGE_H */

/**
 * @example 使用示例:
 * 
 * #include "flash_storage.h"
 * 
 * int main(void) {
 *     // 初始化Flash存储
 *     if(FlashStorage_Init() != FLASH_STORAGE_OK) {
 *         // 初始化失败处理
 *     }
 *     
 *     // 写入第一个数据信息 (负数, 大小1024)
 *     FlashStorage_WriteData1(1, 1024);
 *     
 *     // 写入第二个数据信息 (正数, 大小2048)  
 *     FlashStorage_WriteData2(0, 2048);
 *     
 *     // 读取数据信息
 *     DataInfo_t data1, data2;
 *     FlashStorage_ReadData1(&data1);
 *     FlashStorage_ReadData2(&data2);
 *     
 *     return 0;
 * }
 */
