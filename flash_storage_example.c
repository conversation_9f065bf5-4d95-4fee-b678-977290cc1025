/**
 * @file flash_storage_example.c
 * @brief STM32F407VET6 Flash存储使用示例
 * <AUTHOR> (米醋电子工作室)
 * @version 1.0
 * @date 2025-01-30
 */

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx.h"
#include "flash_storage.h"
#include <stdio.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void GPIO_Config(void);
void USART_Config(void);
void Error_Handler(void);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief 系统时钟配置
 */
void SystemClock_Config(void)
{
    // 这里应该配置系统时钟到168MHz
    // 具体实现根据您的项目需求
}

/**
 * @brief GPIO配置
 */
void GPIO_Config(void)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能GPIOD时钟 (用于LED指示)
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOD, ENABLE);
    
    // 配置PD12-PD15为输出 (STM32F407-DISCO板载LED)
    GPIO_InitStruct.GPIO_Pin = GPIO_Pin_12 | GPIO_Pin_13 | GPIO_Pin_14 | GPIO_Pin_15;
    GPIO_InitStruct.GPIO_Mode = GPIO_Mode_OUT;
    GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;
    GPIO_InitStruct.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIO_Init(GPIOD, &GPIO_InitStruct);
}

/**
 * @brief USART配置 (可选，用于调试输出)
 */
void USART_Config(void)
{
    // 这里可以配置USART用于调试输出
    // 具体实现根据您的硬件连接
}

/**
 * @brief 错误处理函数
 */
void Error_Handler(void)
{
    // 错误指示：红色LED闪烁
    while(1) {
        GPIO_SetBits(GPIOD, GPIO_Pin_14);   // 红色LED亮
        for(volatile int i = 0; i < 1000000; i++);
        GPIO_ResetBits(GPIOD, GPIO_Pin_14); // 红色LED灭
        for(volatile int i = 0; i < 1000000; i++);
    }
}

/**
 * @brief 成功指示函数
 */
void Success_Indication(void)
{
    // 成功指示：绿色LED常亮
    GPIO_SetBits(GPIOD, GPIO_Pin_12);   // 绿色LED亮
}

/**
 * @brief Flash存储测试函数
 */
void FlashStorage_Test(void)
{
    FlashStorage_Status status;
    DataInfo_t data1, data2;
    
    printf("=== Flash存储测试开始 ===\r\n");
    
    // 1. 初始化Flash存储
    printf("1. 初始化Flash存储...\r\n");
    status = FlashStorage_Init();
    if(status != FLASH_STORAGE_OK) {
        printf("   初始化失败! 错误码: %d\r\n", status);
        Error_Handler();
    }
    printf("   初始化成功!\r\n");
    
    // 2. 检查存储区域是否为空
    printf("2. 检查存储区域状态...\r\n");
    if(FlashStorage_IsEmpty()) {
        printf("   存储区域为空\r\n");
    } else {
        printf("   存储区域有数据，读取现有数据:\r\n");
        FlashStorage_ReadData1(&data1);
        FlashStorage_ReadData2(&data2);
        printf("   数据1: 符号=%d, 大小=%lu\r\n", data1.sign, data1.size);
        printf("   数据2: 符号=%d, 大小=%lu\r\n", data2.sign, data2.size);
    }
    
    // 3. 擦除存储区域 (注意：这会擦除整个Sector 7!)
    printf("3. 擦除存储区域...\r\n");
    status = FlashStorage_EraseStorage();
    if(status != FLASH_STORAGE_OK) {
        printf("   擦除失败! 错误码: %d\r\n", status);
        Error_Handler();
    }
    printf("   擦除成功!\r\n");
    
    // 4. 写入测试数据
    printf("4. 写入测试数据...\r\n");
    
    // 写入第一个数据 (负数, 大小1024)
    printf("   写入数据1: 符号=1(负数), 大小=1024\r\n");
    status = FlashStorage_WriteData1(1, 1024);
    if(status != FLASH_STORAGE_OK) {
        printf("   写入数据1失败! 错误码: %d\r\n", status);
        Error_Handler();
    }
    
    // 写入第二个数据 (正数, 大小2048)
    printf("   写入数据2: 符号=0(正数), 大小=2048\r\n");
    status = FlashStorage_WriteData2(0, 2048);
    if(status != FLASH_STORAGE_OK) {
        printf("   写入数据2失败! 错误码: %d\r\n", status);
        Error_Handler();
    }
    printf("   数据写入成功!\r\n");
    
    // 5. 读取并验证数据
    printf("5. 读取并验证数据...\r\n");
    
    status = FlashStorage_ReadData1(&data1);
    if(status != FLASH_STORAGE_OK) {
        printf("   读取数据1失败! 错误码: %d\r\n", status);
        Error_Handler();
    }
    
    status = FlashStorage_ReadData2(&data2);
    if(status != FLASH_STORAGE_OK) {
        printf("   读取数据2失败! 错误码: %d\r\n", status);
        Error_Handler();
    }
    
    printf("   读取结果:\r\n");
    printf("   数据1: 符号=%d, 大小=%lu\r\n", data1.sign, data1.size);
    printf("   数据2: 符号=%d, 大小=%lu\r\n", data2.sign, data2.size);
    
    // 6. 验证数据正确性
    printf("6. 验证数据正确性...\r\n");
    if(data1.sign == 1 && data1.size == 1024 && 
       data2.sign == 0 && data2.size == 2048) {
        printf("   数据验证成功! 所有测试通过!\r\n");
        Success_Indication();
    } else {
        printf("   数据验证失败!\r\n");
        printf("   期望: 数据1(1,1024), 数据2(0,2048)\r\n");
        printf("   实际: 数据1(%d,%lu), 数据2(%d,%lu)\r\n", 
               data1.sign, data1.size, data2.sign, data2.size);
        Error_Handler();
    }
    
    printf("=== Flash存储测试完成 ===\r\n");
}

/**
 * @brief 主函数
 */
int main(void)
{
    // 系统初始化
    SystemInit();
    SystemClock_Config();
    
    // 外设初始化
    GPIO_Config();
    USART_Config();
    
    printf("\r\n");
    printf("========================================\r\n");
    printf("  STM32F407VET6 Flash存储测试程序\r\n");
    printf("  作者: Alex (米醋电子工作室)\r\n");
    printf("  版本: 1.0\r\n");
    printf("========================================\r\n");
    
    // 运行Flash存储测试
    FlashStorage_Test();
    
    // 主循环
    while(1) {
        // 程序正常运行指示：蓝色LED闪烁
        GPIO_SetBits(GPIOD, GPIO_Pin_15);   // 蓝色LED亮
        for(volatile int i = 0; i < 5000000; i++);
        GPIO_ResetBits(GPIOD, GPIO_Pin_15); // 蓝色LED灭
        for(volatile int i = 0; i < 5000000; i++);
    }
}

/**
 * @brief 实际应用示例
 */
void Application_Example(void)
{
    DataInfo_t sensor_data1, sensor_data2;
    
    // 初始化Flash存储
    if(FlashStorage_Init() != FLASH_STORAGE_OK) {
        Error_Handler();
    }
    
    // 假设从传感器读取数据
    int32_t sensor1_value = -1500;  // 传感器1读数 (负值)
    uint32_t sensor1_size = 1500;   // 数据大小
    
    int32_t sensor2_value = 2500;   // 传感器2读数 (正值)  
    uint32_t sensor2_size = 2500;   // 数据大小
    
    // 存储传感器1数据信息
    uint8_t sensor1_sign = (sensor1_value < 0) ? 1 : 0;
    FlashStorage_WriteData1(sensor1_sign, sensor1_size);
    
    // 存储传感器2数据信息
    uint8_t sensor2_sign = (sensor2_value < 0) ? 1 : 0;
    FlashStorage_WriteData2(sensor2_sign, sensor2_size);
    
    // 系统重启后读取数据
    FlashStorage_ReadData1(&sensor_data1);
    FlashStorage_ReadData2(&sensor_data2);
    
    // 根据符号位恢复原始值的符号
    int32_t restored_value1 = sensor_data1.sign ? -(int32_t)sensor_data1.size : (int32_t)sensor_data1.size;
    int32_t restored_value2 = sensor_data2.sign ? -(int32_t)sensor_data2.size : (int32_t)sensor_data2.size;
    
    // 使用恢复的数据...
}
