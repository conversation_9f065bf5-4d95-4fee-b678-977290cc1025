--cpu=Cortex-M4.fp.sp
"23e\startup_stm32f407xx.o"
"23e\main.o"
"23e\gpio.o"
"23e\tim.o"
"23e\usart.o"
"23e\stm32f4xx_it.o"
"23e\stm32f4xx_hal_msp.o"
"23e\stm32f4xx_hal_tim.o"
"23e\stm32f4xx_hal_tim_ex.o"
"23e\stm32f4xx_hal_rcc.o"
"23e\stm32f4xx_hal_rcc_ex.o"
"23e\stm32f4xx_hal_flash.o"
"23e\stm32f4xx_hal_flash_ex.o"
"23e\stm32f4xx_hal_flash_ramfunc.o"
"23e\stm32f4xx_hal_gpio.o"
"23e\stm32f4xx_hal_dma_ex.o"
"23e\stm32f4xx_hal_dma.o"
"23e\stm32f4xx_hal_pwr.o"
"23e\stm32f4xx_hal_pwr_ex.o"
"23e\stm32f4xx_hal_cortex.o"
"23e\stm32f4xx_hal.o"
"23e\stm32f4xx_hal_exti.o"
"23e\stm32f4xx_hal_uart.o"
"23e\system_stm32f4xx.o"
"23e\scheduler.o"
"23e\usart_app.o"
"23e\interrupt.o"
"23e\pid.o"
"23e\app_motor.o"
"23e\app_point2d.o"
"23e\flash_storage.o"
"23e\uart.o"
"23e\emm_v5.o"
--strict --scatter "23E\23E.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "23E.map" -o 23E\23E.axf