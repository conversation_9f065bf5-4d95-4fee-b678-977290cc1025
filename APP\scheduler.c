#include "scheduler.h"
#include "usart_app.h"

uint8_t Key_<PERSON>,Key_Down,Key_Up,Key_Old;
uint8_t flag;
int32_t test1 = 200;
int32_t test2 = -200;
void Led_Proc()
{
		if(flag) HAL_GPIO_WritePin(GPIOA,GPIO_PIN_1,GPIO_PIN_SET);
		else HAL_GPIO_WritePin(GPIOA,GPIO_PIN_1,GPIO_PIN_RESET);
}

uint8_t Key_Read()
{
	uint8_t temp = 0;
	
	if(HAL_GPIO_ReadPin(KEY1_GPIO_Port,KEY1_Pin) == RESET) temp = 1;
	
	return temp;
}

void Key_Proc()
{
	Key_Val = Key_Read();
	Key_Down = Key_Val & ( Key_Val ^ Key_Old);
	Key_Up = ~Key_Val & ( Key_Val ^ Key_Old);
	Key_Old = Key_Val;
	
	if(Key_Down == 1) 
	{

		FlashStorage_GetData1Value(&test1);
		FlashStorage_GetData2Value(&test2);
		my_printf(&huart1,"set ok\r\n");
		
	}
}
uint8_t task_num;

typedef struct
{
	void(*task_func)(void);
	uint32_t rate_ms;
	uint32_t last_run;
}task_t;


void test_task()
{
	Emm_V5_Read_Sys_Params(&huart2, 1, S_MAICHONG);
	Emm_V5_Read_Sys_Params(&huart3, 1, S_MAICHONG);
}

void test_task2()
{
	Emm_V5_Read_Sys_Params(&huart2, 1, S_FLAG);
	Emm_V5_Read_Sys_Params(&huart3, 1, S_FLAG);
//	my_printf(&huart1, "%d\r\n", step_y_flag);
}

void test_task3()
{

    
}

static task_t scheduler_task[] =
{
	{Led_Proc, 1,0},
	{Key_Proc, 10,0},
	{uart_task, 5,0},   
	{test_task, 50, 0},
	{test_task2, 100, 0},
	{test_task3, 10000, 0},
};


void scheduler_init(void)
{
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

void scheduler_run(void)
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint32_t now_time = HAL_GetTick();

        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            scheduler_task[i].last_run = now_time;

            scheduler_task[i].task_func();
        }
    }
}

