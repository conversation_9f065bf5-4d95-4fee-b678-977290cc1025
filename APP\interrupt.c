#include "interrupt.h"

#define STATE_DELAY_MS 200  // 状态延迟时间200ms
#define WAIT_DELAY_MS 200  // 等待到位延迟时间200ms

struct state_machine State_Machine;

uint32_t state_start_time = 0;  // 状态开始时间戳

void task2_proc(void);
void task3_proc(void);
int16_t count1;
int16_t count2;
void State_Machine_init()
{
    State_Machine.MAIN_STATE = STATE_IDLE;
    State_Machine.STATE_TASK2 = task2_state0;
    State_Machine.STATE_TASK3 = task3_state0;
    State_Machine.STATE_TASK4 = STATE_IDLE;//没改
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
	if(htim -> Instance == TIM6)//20ms进入一次中断
	{
		switch(State_Machine.MAIN_STATE)
		{
			case STATE_IDLE://IDLE
				
			break;
			case TASK_2://第二问逻辑
			{
				task2_proc();
			}
			break;
			case TASK_3: //第三问逻辑
			{
				task3_proc();
			}
			break;
			case TASK_4: //第四问逻辑
				
			break;
			default:
				
			break;
		}
		
	}
	
}
void task3_proc()
{
	switch(State_Machine.STATE_TASK3)
	{
		case task3_state0:
			track_control();
			//my_printf(&huart1,"%.3f,%.3f\r\n",motorA_pid.output,motorB_pid.output);
		break;
	}
}
void task2_proc()
{	
	
}
