/**
 * @file flash_storage.c
 * @brief STM32F407VET6 Flash存储管理实现文件
 * <AUTHOR> (米醋电子工作室)
 * @version 1.0
 * @date 2025-01-30
 */

/* Includes ------------------------------------------------------------------*/
#include "flash_storage.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
static FlashStorage_Status Flash_WaitForOperation(uint32_t timeout);
static FlashStorage_Status Flash_WriteWord(uint32_t address, uint32_t data);
static uint32_t Flash_ReadWord(uint32_t address);

/* Private functions ---------------------------------------------------------*/

/**
 * @brief 等待Flash操作完成
 * @param timeout: 超时时间
 * @retval FlashStorage_Status 操作状态
 */
static FlashStorage_Status Flash_WaitForOperation(uint32_t timeout)
{
    uint32_t counter = 0;
    
    while(FLASH_GetStatus() == FLASH_BUSY) {
        counter++;
        if(counter > timeout) {
            return FLASH_STORAGE_TIMEOUT;
        }
    }
    
    if(FLASH_GetStatus() != FLASH_COMPLETE) {
        return FLASH_STORAGE_ERROR;
    }
    
    return FLASH_STORAGE_OK;
}

/**
 * @brief 向Flash写入一个字(32位)
 * @param address: 写入地址
 * @param data: 写入数据
 * @retval FlashStorage_Status 操作状态
 */
static FlashStorage_Status Flash_WriteWord(uint32_t address, uint32_t data)
{
    FLASH_Status flash_status;
    
    // 解锁Flash
    FLASH_Unlock();
    
    // 清除标志位
    FLASH_ClearFlag(FLASH_FLAG_EOP | FLASH_FLAG_OPERR | FLASH_FLAG_WRPERR | 
                    FLASH_FLAG_PGAERR | FLASH_FLAG_PGPERR | FLASH_FLAG_PGSERR);
    
    // 写入数据
    flash_status = FLASH_ProgramWord(address, data);
    
    // 锁定Flash
    FLASH_Lock();
    
    if(flash_status != FLASH_COMPLETE) {
        return FLASH_STORAGE_ERROR;
    }
    
    return FLASH_STORAGE_OK;
}

/**
 * @brief 从Flash读取一个字(32位)
 * @param address: 读取地址
 * @retval uint32_t 读取的数据
 */
static uint32_t Flash_ReadWord(uint32_t address)
{
    return (*(__IO uint32_t*)address);
}

/* Exported functions --------------------------------------------------------*/

/**
 * @brief 初始化Flash存储模块
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_Init(void)
{
    // 检查Flash是否已解锁
    if(FLASH->CR & FLASH_CR_LOCK) {
        FLASH_Unlock();
        FLASH_Lock();
    }
    
    return FLASH_STORAGE_OK;
}

/**
 * @brief 写入第一个数据的信息
 * @param sign: 符号位 (0=正数, 1=负数)
 * @param size: 数据大小
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_WriteData1(uint8_t sign, uint32_t size)
{
    FlashStorage_Status status;
    
    // 参数检查
    if(!IS_VALID_SIGN(sign) || !IS_VALID_SIZE(size)) {
        return FLASH_STORAGE_INVALID_PARAM;
    }
    
    // 写入符号位
    status = Flash_WriteWord(DATA1_SIGN_ADDR, (uint32_t)sign);
    if(status != FLASH_STORAGE_OK) {
        return status;
    }
    
    // 写入大小
    status = Flash_WriteWord(DATA1_SIZE_ADDR, size);
    if(status != FLASH_STORAGE_OK) {
        return status;
    }
    
    return FLASH_STORAGE_OK;
}

/**
 * @brief 写入第二个数据的信息
 * @param sign: 符号位 (0=正数, 1=负数)
 * @param size: 数据大小
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_WriteData2(uint8_t sign, uint32_t size)
{
    FlashStorage_Status status;
    
    // 参数检查
    if(!IS_VALID_SIGN(sign) || !IS_VALID_SIZE(size)) {
        return FLASH_STORAGE_INVALID_PARAM;
    }
    
    // 写入符号位
    status = Flash_WriteWord(DATA2_SIGN_ADDR, (uint32_t)sign);
    if(status != FLASH_STORAGE_OK) {
        return status;
    }
    
    // 写入大小
    status = Flash_WriteWord(DATA2_SIZE_ADDR, size);
    if(status != FLASH_STORAGE_OK) {
        return status;
    }
    
    return FLASH_STORAGE_OK;
}

/**
 * @brief 读取第一个数据的信息
 * @param data_info: 指向数据信息结构体的指针
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_ReadData1(DataInfo_t *data_info)
{
    if(data_info == NULL) {
        return FLASH_STORAGE_INVALID_PARAM;
    }
    
    data_info->sign = (uint8_t)Flash_ReadWord(DATA1_SIGN_ADDR);
    data_info->size = Flash_ReadWord(DATA1_SIZE_ADDR);
    
    return FLASH_STORAGE_OK;
}

/**
 * @brief 读取第二个数据的信息
 * @param data_info: 指向数据信息结构体的指针
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_ReadData2(DataInfo_t *data_info)
{
    if(data_info == NULL) {
        return FLASH_STORAGE_INVALID_PARAM;
    }
    
    data_info->sign = (uint8_t)Flash_ReadWord(DATA2_SIGN_ADDR);
    data_info->size = Flash_ReadWord(DATA2_SIZE_ADDR);
    
    return FLASH_STORAGE_OK;
}

/**
 * @brief 擦除存储区域
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_EraseStorage(void)
{
    FLASH_Status flash_status;
    
    // 解锁Flash
    FLASH_Unlock();
    
    // 清除标志位
    FLASH_ClearFlag(FLASH_FLAG_EOP | FLASH_FLAG_OPERR | FLASH_FLAG_WRPERR | 
                    FLASH_FLAG_PGAERR | FLASH_FLAG_PGPERR | FLASH_FLAG_PGSERR);
    
    // 擦除Sector 7 (注意：这会擦除整个扇区!)
    flash_status = FLASH_EraseSector(FLASH_Sector_7, FLASH_VOLTAGE_RANGE);
    
    // 锁定Flash
    FLASH_Lock();
    
    if(flash_status != FLASH_COMPLETE) {
        return FLASH_STORAGE_ERROR;
    }
    
    return FLASH_STORAGE_OK;
}

/**
 * @brief 检查存储区域是否为空
 * @retval bool true=空, false=非空
 */
bool FlashStorage_IsEmpty(void)
{
    uint32_t data1_sign = Flash_ReadWord(DATA1_SIGN_ADDR);
    uint32_t data1_size = Flash_ReadWord(DATA1_SIZE_ADDR);
    uint32_t data2_sign = Flash_ReadWord(DATA2_SIGN_ADDR);
    uint32_t data2_size = Flash_ReadWord(DATA2_SIZE_ADDR);
    
    // Flash擦除后所有位都是1 (0xFFFFFFFF)
    return (data1_sign == 0xFFFFFFFF && data1_size == 0xFFFFFFFF && 
            data2_sign == 0xFFFFFFFF && data2_size == 0xFFFFFFFF);
}

/**
 * @brief 获取Flash存储区域状态信息
 * @param data1_info: 第一个数据信息
 * @param data2_info: 第二个数据信息
 * @retval FlashStorage_Status 操作状态
 */
FlashStorage_Status FlashStorage_GetStatus(DataInfo_t *data1_info, DataInfo_t *data2_info)
{
    if(data1_info == NULL || data2_info == NULL) {
        return FLASH_STORAGE_INVALID_PARAM;
    }
    
    FlashStorage_ReadData1(data1_info);
    FlashStorage_ReadData2(data2_info);
    
    return FLASH_STORAGE_OK;
}
