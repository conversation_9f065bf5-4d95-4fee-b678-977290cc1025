# STM32F407VET6 Flash存储方案使用说明

## 概述
本方案为STM32F407VET6设计了安全的Flash存储功能，用于存储2个数据的符号位和大小信息。选择了Sector 7的最后16字节作为存储区域，确保不影响程序正常运行。

## 技术规格

### 硬件信息
- **MCU型号**: STM32F407VET6
- **Flash大小**: 512KB (0x08000000 - 0x0807FFFF)
- **存储位置**: Sector 7末尾16字节 (0x0807FFF0 - 0x0807FFFF)

### 存储布局
```
地址范围: 0x0807FFF0 - 0x0807FFFF (16字节)

0x0807FFF0: 第一个数据符号位 (4字节)
0x0807FFF4: 第一个数据大小   (4字节)  
0x0807FFF8: 第二个数据符号位 (4字节)
0x0807FFFC: 第二个数据大小   (4字节)
```

## 文件说明

### 1. flash_storage.h
头文件，包含所有函数声明、常量定义和数据结构。

### 2. flash_storage.c  
实现文件，包含所有函数的具体实现。

## 使用方法

### 1. 项目集成
将以下文件添加到您的STM32项目中：
- `flash_storage.h`
- `flash_storage.c`

### 2. 包含头文件
```c
#include "flash_storage.h"
```

### 3. 基本使用示例
```c
#include "flash_storage.h"

int main(void) {
    // 系统初始化
    SystemInit();
    
    // 初始化Flash存储模块
    if(FlashStorage_Init() != FLASH_STORAGE_OK) {
        // 初始化失败处理
        while(1);
    }
    
    // 写入第一个数据信息 (负数, 大小1024)
    if(FlashStorage_WriteData1(1, 1024) != FLASH_STORAGE_OK) {
        // 写入失败处理
    }
    
    // 写入第二个数据信息 (正数, 大小2048)
    if(FlashStorage_WriteData2(0, 2048) != FLASH_STORAGE_OK) {
        // 写入失败处理  
    }
    
    // 读取数据信息
    DataInfo_t data1, data2;
    FlashStorage_ReadData1(&data1);
    FlashStorage_ReadData2(&data2);
    
    // 使用读取的数据
    if(data1.sign == 1) {
        // 第一个数据是负数，大小为data1.size
    }
    
    while(1) {
        // 主循环
    }
}
```

## API函数说明

### 初始化函数
```c
FlashStorage_Status FlashStorage_Init(void);
```
- **功能**: 初始化Flash存储模块
- **返回值**: 操作状态

### 写入函数
```c
FlashStorage_Status FlashStorage_WriteData1(uint8_t sign, uint32_t size);
FlashStorage_Status FlashStorage_WriteData2(uint8_t sign, uint32_t size);
```
- **功能**: 写入数据信息
- **参数**: 
  - `sign`: 符号位 (0=正数, 1=负数)
  - `size`: 数据大小
- **返回值**: 操作状态

### 读取函数
```c
FlashStorage_Status FlashStorage_ReadData1(DataInfo_t *data_info);
FlashStorage_Status FlashStorage_ReadData2(DataInfo_t *data_info);
```
- **功能**: 读取数据信息
- **参数**: `data_info`: 指向数据信息结构体的指针
- **返回值**: 操作状态

### 工具函数
```c
FlashStorage_Status FlashStorage_EraseStorage(void);
bool FlashStorage_IsEmpty(void);
FlashStorage_Status FlashStorage_GetStatus(DataInfo_t *data1_info, DataInfo_t *data2_info);
```

## 状态码说明
```c
typedef enum {
    FLASH_STORAGE_OK = 0,           // 操作成功
    FLASH_STORAGE_ERROR,            // 操作失败
    FLASH_STORAGE_TIMEOUT,          // 操作超时
    FLASH_STORAGE_INVALID_PARAM     // 参数无效
} FlashStorage_Status;
```

## 注意事项

### 1. 安全性
- 选择的存储地址位于Flash末尾，不会影响程序代码
- 使用前请确保您的程序代码不会占用到0x0807FFF0以后的地址

### 2. 擦除操作
- `FlashStorage_EraseStorage()`会擦除整个Sector 7
- 如果您的程序代码使用了Sector 7的其他部分，请谨慎使用此函数

### 3. 电源要求
- Flash写入操作需要稳定的电源供应
- 建议在写入前检查电源电压是否在2.7V-3.6V范围内

### 4. 写入次数限制
- Flash有写入次数限制（通常10,000次擦写周期）
- 频繁写入可能影响Flash寿命

## 编译要求
- 需要STM32F4xx标准外设库
- 确保包含以下头文件：
  - `stm32f4xx.h`
  - `stm32f4xx_flash.h`

## 版权信息
- **作者**: Alex (米醋电子工作室)
- **版本**: 1.0
- **日期**: 2025-01-30
- **版权**: 米醋电子工作室

## 技术支持
如有问题，请联系技术支持团队。
